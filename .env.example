# api
ENV=''
API_HOST=''
API_PORT=''

# database
MASTER_DB_HOST=''
MASTER_DB_PORT=''
MASTER_DB_NAME=''
MASTER_USERNAME=''
MASTER_PASSWORD=''

# auth
JWT_SECRET=''
JWT_ACCESS_TOKEN_EXPIRY='' # eg: 24h, 48h
JWT_REFRESH_TOKEN_EXPIRY='' #eg: 168h (for 1 week), 336h (for 2 weeks)

# logging
ERROR_LOG_FILE='' # eg: errors.json
ERROR_LOG_MAX_SIZE='' # in megabytes, eg: 10
ERROR_LOG_MAX_AGE='' # in days, eg: 28
ERROR_LOG_MAX_BACKUPS='' # number of old log files to keep, eg: 3
ERROR_LOG_COMPRESS='' # eg: true, false

# caching
CACHE_EXPIRY='' # supported time units: h, m, s eg: 5m, 1h, 30s

# business
POS_PLATFORM_ID='' # eg: 9