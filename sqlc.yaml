version: "2"
sql:
  - engine: "mysql"
    queries: "database/queries"
    schema: "database/schema"
    gen:
      go:
        package: "db"
        out: "database/sqlc"
        sql_package: "database/sql"
        emit_interface: true
        emit_json_tags: true
        emit_empty_slices: true
        emit_params_struct_pointers: true
        emit_result_struct_pointers: true
        json_tags_case_style: "camel"
        output_models_file_name: "models.go"
        output_querier_file_name: "querier.go"
        output_files_suffix: "_query"
