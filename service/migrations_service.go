package service

import (
	"context"

	"github.com/Delivergate-Dev/pos-service-golang/database"
)

type IMigrationsService interface {
	ApplyMigrations(ctx context.Context) error
}

type MigrationsService struct{}

func NewMigrationsService() *MigrationsService {
	return &MigrationsService{}
}

func (s *MigrationsService) ApplyMigrations(ctx context.Context) error {
	if err := database.RunTenantMigrations(ctx); err != nil {
		return err
	}
	return nil
}
