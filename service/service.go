package service

import (
	"github.com/Delivergate-Dev/pos-service-golang/config"
	"github.com/Delivergate-Dev/pos-service-golang/database"
)

// ServiceRegistry holds all application services
type ServiceRegistry struct {
	User       IUserService
	Auth       IAuthService
	Migrations IMigrationsService
	Menu       *ItemCategoryService
}

// NewServiceRegistry creates and returns a new instance of ServiceRegistry
func NewServiceRegistry(tenantManager database.TenantManager, cfg *config.Config, cacheStore database.Cache) *ServiceRegistry {
	return &ServiceRegistry{
		User:       NewUserService(tenantManager, cacheStore),
		Auth:       NewAuthService(tenantManager, cfg),
		Migrations: NewMigrationsService(),
		Menu:       NewItemCategoryService(tenantManager, cfg),
	}
}
