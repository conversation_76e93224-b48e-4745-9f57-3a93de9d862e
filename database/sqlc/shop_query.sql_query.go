// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: shop_query.sql

package db

import (
	"context"
)

const getShopByID = `-- name: GetShopByID :one
SELECT id, name, franchise_id, code, email, address, branch, contact_no, business_reg_no, socket_code, status, order_status, last_updated_menu, last_pos_synced, service_availability, created_at, updated_at, minimum_amount_for_free_delivery, minimum_amount_for_delivery, one_time_promotion_value, one_time_promotion_spend_amount, latitude, longitude, google_location_url, one_time_promotion_type, maximum_promotion_value, selected_menu, is_default, country_code, timezone, currency, currency_code, print_receipt_accept, number_of_receipt, delivery_platform_enable, run_test_order, has_cash_payment, has_card_payment, tbl_commission_per_head, tbl_ref_commission_per_head, transfer_destination, delivergate_account, printer_status, deleted_at, has_collection_points, collection_points_note, user_interaction_status, interaction_timeout FROM shop WHERE id = ?
`

func (q *Queries) GetShopByID(ctx context.Context, id uint64) (*Shop, error) {
	row := q.db.QueryRowContext(ctx, getShopByID, id)
	var i Shop
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.FranchiseID,
		&i.Code,
		&i.Email,
		&i.Address,
		&i.Branch,
		&i.ContactNo,
		&i.BusinessRegNo,
		&i.SocketCode,
		&i.Status,
		&i.OrderStatus,
		&i.LastUpdatedMenu,
		&i.LastPosSynced,
		&i.ServiceAvailability,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.MinimumAmountForFreeDelivery,
		&i.MinimumAmountForDelivery,
		&i.OneTimePromotionValue,
		&i.OneTimePromotionSpendAmount,
		&i.Latitude,
		&i.Longitude,
		&i.GoogleLocationUrl,
		&i.OneTimePromotionType,
		&i.MaximumPromotionValue,
		&i.SelectedMenu,
		&i.IsDefault,
		&i.CountryCode,
		&i.Timezone,
		&i.Currency,
		&i.CurrencyCode,
		&i.PrintReceiptAccept,
		&i.NumberOfReceipt,
		&i.DeliveryPlatformEnable,
		&i.RunTestOrder,
		&i.HasCashPayment,
		&i.HasCardPayment,
		&i.TblCommissionPerHead,
		&i.TblRefCommissionPerHead,
		&i.TransferDestination,
		&i.DelivergateAccount,
		&i.PrinterStatus,
		&i.DeletedAt,
		&i.HasCollectionPoints,
		&i.CollectionPointsNote,
		&i.UserInteractionStatus,
		&i.InteractionTimeout,
	)
	return &i, err
}
