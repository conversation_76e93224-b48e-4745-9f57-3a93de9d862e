// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: deliveryPlatform_query.sql

package db

import (
	"context"
	"database/sql"
)

const getDeliveryPlatform = `-- name: GetDeliveryPlatform :one
SELECT id, platform_id, name, webshop_brand_id, api_url, auth_url, api_parameters, logo, status, created_at, updated_at, outlet_code, site_id, branch_id, access_token, primary_color, can_upload, auto_accepting, franchise_id, outlet_id, menu_id, menu_upload_status, store_status, available_from, tender_types, is_master, parent_platform, prep_time, own_driver, menu_published_at, webshop_setup_status, has_cash_payment, has_card_payment, selected_menu, deleted_at FROM delivery_platform WHERE outlet_id = ? AND webshop_brand_id = ? AND platform_id = ?
`

type GetDeliveryPlatformParams struct {
	OutletID       sql.NullInt64 `json:"outletId"`
	WebshopBrandID sql.NullInt32 `json:"webshopBrandId"`
	PlatformID     sql.NullInt32 `json:"platformId"`
}

func (q *Queries) GetDeliveryPlatform(ctx context.Context, arg *GetDeliveryPlatformParams) (*DeliveryPlatform, error) {
	row := q.db.QueryRowContext(ctx, getDeliveryPlatform, arg.OutletID, arg.WebshopBrandID, arg.PlatformID)
	var i DeliveryPlatform
	err := row.Scan(
		&i.ID,
		&i.PlatformID,
		&i.Name,
		&i.WebshopBrandID,
		&i.ApiUrl,
		&i.AuthUrl,
		&i.ApiParameters,
		&i.Logo,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.OutletCode,
		&i.SiteID,
		&i.BranchID,
		&i.AccessToken,
		&i.PrimaryColor,
		&i.CanUpload,
		&i.AutoAccepting,
		&i.FranchiseID,
		&i.OutletID,
		&i.MenuID,
		&i.MenuUploadStatus,
		&i.StoreStatus,
		&i.AvailableFrom,
		&i.TenderTypes,
		&i.IsMaster,
		&i.ParentPlatform,
		&i.PrepTime,
		&i.OwnDriver,
		&i.MenuPublishedAt,
		&i.WebshopSetupStatus,
		&i.HasCashPayment,
		&i.HasCardPayment,
		&i.SelectedMenu,
		&i.DeletedAt,
	)
	return &i, err
}
