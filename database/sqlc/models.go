// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"database/sql"
	"time"
)

type DeliveryPlatform struct {
	ID                 uint64         `json:"id"`
	PlatformID         sql.NullInt32  `json:"platformId"`
	Name               string         `json:"name"`
	WebshopBrandID     sql.NullInt32  `json:"webshopBrandId"`
	ApiUrl             sql.NullString `json:"apiUrl"`
	AuthUrl            sql.NullString `json:"authUrl"`
	ApiParameters      sql.NullString `json:"apiParameters"`
	Logo               sql.NullString `json:"logo"`
	Status             string         `json:"status"`
	CreatedAt          sql.NullTime   `json:"createdAt"`
	UpdatedAt          sql.NullTime   `json:"updatedAt"`
	OutletCode         sql.NullString `json:"outletCode"`
	SiteID             sql.NullString `json:"siteId"`
	BranchID           sql.NullString `json:"branchId"`
	AccessToken        sql.NullString `json:"accessToken"`
	PrimaryColor       sql.NullString `json:"primaryColor"`
	CanUpload          bool           `json:"canUpload"`
	AutoAccepting      bool           `json:"autoAccepting"`
	FranchiseID        int32          `json:"franchiseId"`
	OutletID           sql.NullInt64  `json:"outletId"`
	MenuID             sql.NullString `json:"menuId"`
	MenuUploadStatus   sql.NullString `json:"menuUploadStatus"`
	StoreStatus        sql.NullString `json:"storeStatus"`
	AvailableFrom      sql.NullTime   `json:"availableFrom"`
	TenderTypes        sql.NullString `json:"tenderTypes"`
	IsMaster           bool           `json:"isMaster"`
	ParentPlatform     sql.NullInt32  `json:"parentPlatform"`
	PrepTime           sql.NullInt32  `json:"prepTime"`
	OwnDriver          bool           `json:"ownDriver"`
	MenuPublishedAt    sql.NullTime   `json:"menuPublishedAt"`
	WebshopSetupStatus bool           `json:"webshopSetupStatus"`
	HasCashPayment     bool           `json:"hasCashPayment"`
	HasCardPayment     bool           `json:"hasCardPayment"`
	SelectedMenu       sql.NullInt32  `json:"selectedMenu"`
	DeletedAt          sql.NullTime   `json:"deletedAt"`
}

type MainMenuMenu struct {
	ID         uint64       `json:"id"`
	MainMenuID int32        `json:"mainMenuId"`
	MenuID     int32        `json:"menuId"`
	CreatedAt  sql.NullTime `json:"createdAt"`
	UpdatedAt  sql.NullTime `json:"updatedAt"`
}

type PosRefreshToken struct {
	UserID    uint64       `json:"userId"`
	Token     string       `json:"token"`
	ExpiresAt time.Time    `json:"expiresAt"`
	CreatedAt sql.NullTime `json:"createdAt"`
	UpdatedAt sql.NullTime `json:"updatedAt"`
}

type Role struct {
	ID          uint32       `json:"id"`
	Name        string       `json:"name"`
	Description string       `json:"description"`
	CreatedAt   sql.NullTime `json:"createdAt"`
	UpdatedAt   sql.NullTime `json:"updatedAt"`
}

type Shop struct {
	ID                           uint64         `json:"id"`
	Name                         string         `json:"name"`
	FranchiseID                  int32          `json:"franchiseId"`
	Code                         string         `json:"code"`
	Email                        sql.NullString `json:"email"`
	Address                      sql.NullString `json:"address"`
	Branch                       sql.NullString `json:"branch"`
	ContactNo                    sql.NullString `json:"contactNo"`
	BusinessRegNo                sql.NullString `json:"businessRegNo"`
	SocketCode                   sql.NullString `json:"socketCode"`
	Status                       string         `json:"status"`
	OrderStatus                  string         `json:"orderStatus"`
	LastUpdatedMenu              sql.NullInt32  `json:"lastUpdatedMenu"`
	LastPosSynced                sql.NullTime   `json:"lastPosSynced"`
	ServiceAvailability          sql.NullString `json:"serviceAvailability"`
	CreatedAt                    sql.NullTime   `json:"createdAt"`
	UpdatedAt                    sql.NullTime   `json:"updatedAt"`
	MinimumAmountForFreeDelivery sql.NullString `json:"minimumAmountForFreeDelivery"`
	MinimumAmountForDelivery     sql.NullString `json:"minimumAmountForDelivery"`
	OneTimePromotionValue        sql.NullString `json:"oneTimePromotionValue"`
	OneTimePromotionSpendAmount  sql.NullString `json:"oneTimePromotionSpendAmount"`
	Latitude                     sql.NullString `json:"latitude"`
	Longitude                    sql.NullString `json:"longitude"`
	GoogleLocationUrl            sql.NullString `json:"googleLocationUrl"`
	OneTimePromotionType         sql.NullString `json:"oneTimePromotionType"`
	MaximumPromotionValue        string         `json:"maximumPromotionValue"`
	SelectedMenu                 sql.NullInt32  `json:"selectedMenu"`
	IsDefault                    bool           `json:"isDefault"`
	CountryCode                  sql.NullString `json:"countryCode"`
	Timezone                     string         `json:"timezone"`
	Currency                     string         `json:"currency"`
	CurrencyCode                 string         `json:"currencyCode"`
	PrintReceiptAccept           bool           `json:"printReceiptAccept"`
	NumberOfReceipt              int32          `json:"numberOfReceipt"`
	DeliveryPlatformEnable       bool           `json:"deliveryPlatformEnable"`
	RunTestOrder                 bool           `json:"runTestOrder"`
	HasCashPayment               bool           `json:"hasCashPayment"`
	HasCardPayment               bool           `json:"hasCardPayment"`
	TblCommissionPerHead         string         `json:"tblCommissionPerHead"`
	TblRefCommissionPerHead      string         `json:"tblRefCommissionPerHead"`
	TransferDestination          sql.NullString `json:"transferDestination"`
	DelivergateAccount           bool           `json:"delivergateAccount"`
	PrinterStatus                bool           `json:"printerStatus"`
	DeletedAt                    sql.NullTime   `json:"deletedAt"`
	HasCollectionPoints          bool           `json:"hasCollectionPoints"`
	CollectionPointsNote         sql.NullString `json:"collectionPointsNote"`
	UserInteractionStatus        bool           `json:"userInteractionStatus"`
	InteractionTimeout           sql.NullInt32  `json:"interactionTimeout"`
}

type Tenant struct {
	ID                  uint64       `json:"id"`
	Host                string       `json:"host"`
	Port                string       `json:"port"`
	DatabaseName        string       `json:"databaseName"`
	XTenantCode         string       `json:"xTenantCode"`
	Username            string       `json:"username"`
	Password            string       `json:"password"`
	ServerClientID      int32        `json:"serverClientId"`
	ServerClientSecret  string       `json:"serverClientSecret"`
	SiteName            string       `json:"siteName"`
	PrdDomain           string       `json:"prdDomain"`
	CreatedAt           sql.NullTime `json:"createdAt"`
	UpdatedAt           sql.NullTime `json:"updatedAt"`
	HasWebshop          bool         `json:"hasWebshop"`
	EnableDusk          bool         `json:"enableDusk"`
	DuskPort            string       `json:"duskPort"`
	SyncStatus          bool         `json:"syncStatus"`
	SyncWebshopStatus   bool         `json:"syncWebshopStatus"`
	HasMultishop        bool         `json:"hasMultishop"`
	SendReports         bool         `json:"sendReports"`
	RetryAuthentication bool         `json:"retryAuthentication"`
}

type User struct {
	ID              uint64         `json:"id"`
	Name            string         `json:"name"`
	LastName        sql.NullString `json:"lastName"`
	Email           string         `json:"email"`
	Address         sql.NullString `json:"address"`
	ContactNo       sql.NullString `json:"contactNo"`
	EmailVerifiedAt sql.NullTime   `json:"emailVerifiedAt"`
	Password        string         `json:"password"`
	Pin             sql.NullString `json:"pin"`
	Status          sql.NullString `json:"status"`
	RememberToken   sql.NullString `json:"rememberToken"`
	ExpoToken       sql.NullString `json:"expoToken"`
	DeviceID        sql.NullString `json:"deviceId"`
	CreatedAt       sql.NullTime   `json:"createdAt"`
	UpdatedAt       sql.NullTime   `json:"updatedAt"`
}

type UserRole struct {
	ID        uint32       `json:"id"`
	UserID    uint32       `json:"userId"`
	RoleID    uint32       `json:"roleId"`
	CreatedAt sql.NullTime `json:"createdAt"`
	UpdatedAt sql.NullTime `json:"updatedAt"`
}

type WebshopMenu struct {
	ID                 uint64         `json:"id"`
	MainMenuID         int32          `json:"mainMenuId"`
	SubmenuID          sql.NullInt32  `json:"submenuId"`
	PlatformID         sql.NullInt32  `json:"platformId"`
	DeliveryPlatformID int32          `json:"deliveryPlatformId"`
	Status             sql.NullInt32  `json:"status"`
	OutletID           int32          `json:"outletId"`
	Day                sql.NullString `json:"day"`
	From               sql.NullString `json:"from"`
	To                 sql.NullString `json:"to"`
	Menu               sql.NullString `json:"menu"`
	CategoryIds        sql.NullString `json:"categoryIds"`
	CreatedAt          sql.NullTime   `json:"createdAt"`
	UpdatedAt          sql.NullTime   `json:"updatedAt"`
}
