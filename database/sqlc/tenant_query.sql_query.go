// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: tenant_query.sql

package db

import (
	"context"
)

const getTenantInfo = `-- name: GetTenantInfo :one
SELECT host, port, database_name, username, password FROM tenants WHERE x_tenant_code = ?
`

type GetTenantInfoRow struct {
	Host         string `json:"host"`
	Port         string `json:"port"`
	DatabaseName string `json:"databaseName"`
	Username     string `json:"username"`
	Password     string `json:"password"`
}

func (q *Queries) GetTenantInfo(ctx context.Context, xTenantCode string) (*GetTenantInfoRow, error) {
	row := q.db.QueryRowContext(ctx, getTenantInfo, xTenantCode)
	var i GetTenantInfoRow
	err := row.Scan(
		&i.Host,
		&i.Port,
		&i.DatabaseName,
		&i.Username,
		&i.Password,
	)
	return &i, err
}

const getTenants = `-- name: GetTenants :many
SELECT id, host, port, database_name, x_tenant_code, username, password, server_client_id, server_client_secret, site_name, prd_domain, created_at, updated_at, has_webshop, enable_dusk, dusk_port, sync_status, sync_webshop_status, has_multishop, send_reports, retry_authentication FROM tenants
`

func (q *Queries) GetTenants(ctx context.Context) ([]*Tenant, error) {
	rows, err := q.db.QueryContext(ctx, getTenants)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Tenant{}
	for rows.Next() {
		var i Tenant
		if err := rows.Scan(
			&i.ID,
			&i.Host,
			&i.Port,
			&i.DatabaseName,
			&i.XTenantCode,
			&i.Username,
			&i.Password,
			&i.ServerClientID,
			&i.ServerClientSecret,
			&i.SiteName,
			&i.PrdDomain,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.HasWebshop,
			&i.EnableDusk,
			&i.DuskPort,
			&i.SyncStatus,
			&i.SyncWebshopStatus,
			&i.HasMultishop,
			&i.SendReports,
			&i.RetryAuthentication,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
