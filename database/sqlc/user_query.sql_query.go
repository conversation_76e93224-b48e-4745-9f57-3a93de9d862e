// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: user_query.sql

package db

import (
	"context"
	"database/sql"
)

const getUserByEmail = `-- name: GetUserByEmail :one
SELECT id, name, last_name, email, address, contact_no, email_verified_at, password, pin, status, remember_token, expo_token, device_id, created_at, updated_at FROM users WHERE email = ?
`

func (q *Queries) GetUserByEmail(ctx context.Context, email string) (*User, error) {
	row := q.db.QueryRowContext(ctx, getUserByEmail, email)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.LastName,
		&i.Email,
		&i.Address,
		&i.ContactNo,
		&i.EmailVerifiedAt,
		&i.Password,
		&i.Pin,
		&i.Status,
		&i.RememberToken,
		&i.ExpoToken,
		&i.<PERSON>,
		&i.CreatedAt,
		&i.<PERSON>t,
	)
	return &i, err
}

const getUserByID = `-- name: GetUserByID :one
SELECT 
    u.id,
    u.name,
    u.last_name,
    u.email,
    r.id as role_id,
    r.name as role_name
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id 
WHERE u.id = ?
`

type GetUserByIDRow struct {
	ID       uint64         `json:"id"`
	Name     string         `json:"name"`
	LastName sql.NullString `json:"lastName"`
	Email    string         `json:"email"`
	RoleID   sql.NullInt32  `json:"roleId"`
	RoleName sql.NullString `json:"roleName"`
}

func (q *Queries) GetUserByID(ctx context.Context, id uint64) (*GetUserByIDRow, error) {
	row := q.db.QueryRowContext(ctx, getUserByID, id)
	var i GetUserByIDRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.LastName,
		&i.Email,
		&i.RoleID,
		&i.RoleName,
	)
	return &i, err
}

const getUsers = `-- name: GetUsers :many
SELECT 
    u.id,
    u.name,
    u.last_name,
    u.email,
    r.id as role_id,
    r.name as role_name
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id
ORDER BY u.name
`

type GetUsersRow struct {
	ID       uint64         `json:"id"`
	Name     string         `json:"name"`
	LastName sql.NullString `json:"lastName"`
	Email    string         `json:"email"`
	RoleID   sql.NullInt32  `json:"roleId"`
	RoleName sql.NullString `json:"roleName"`
}

func (q *Queries) GetUsers(ctx context.Context) ([]*GetUsersRow, error) {
	rows, err := q.db.QueryContext(ctx, getUsers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetUsersRow{}
	for rows.Next() {
		var i GetUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.LastName,
			&i.Email,
			&i.RoleID,
			&i.RoleName,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
