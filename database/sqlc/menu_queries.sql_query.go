// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: menu_queries.sql

package db

import (
	"context"
	"database/sql"
)

const getCategoryItemsByMenuIdAndShopId = `-- name: GetCategoryItemsByMenuIdAndShopId :one
SELECT id, main_menu_id, submenu_id, platform_id, delivery_platform_id, status, outlet_id, day, ` + "`" + `from` + "`" + `, ` + "`" + `to` + "`" + `, menu, category_ids, created_at, updated_at
FROM webshop_menu wm
WHERE wm.status = 1
  AND wm.main_menu_id = ?
  AND wm.submenu_id IN (
      SELECT menu_id
      FROM main_menu_menu mmm
      WHERE mmm.main_menu_id = ?
  )
  AND wm.delivery_platform_id = ?
  AND wm.outlet_id = ?
  AND wm.day = ?
  AND wm.from <= ?
  AND wm.to >= ?
ORDER BY wm.id DESC
`

type GetCategoryItemsByMenuIdAndShopIdParams struct {
	MainMenuID         int32          `json:"mainMenuId"`
	DeliveryPlatformID int32          `json:"deliveryPlatformId"`
	OutletID           int32          `json:"outletId"`
	Day                sql.NullString `json:"day"`
	From               sql.NullString `json:"from"`
	To                 sql.NullString `json:"to"`
}

func (q *Queries) GetCategoryItemsByMenuIdAndShopId(ctx context.Context, arg *GetCategoryItemsByMenuIdAndShopIdParams) (*WebshopMenu, error) {
	row := q.db.QueryRowContext(ctx, getCategoryItemsByMenuIdAndShopId,
		arg.MainMenuID,
		arg.MainMenuID,
		arg.DeliveryPlatformID,
		arg.OutletID,
		arg.Day,
		arg.From,
		arg.To,
	)
	var i WebshopMenu
	err := row.Scan(
		&i.ID,
		&i.MainMenuID,
		&i.SubmenuID,
		&i.PlatformID,
		&i.DeliveryPlatformID,
		&i.Status,
		&i.OutletID,
		&i.Day,
		&i.From,
		&i.To,
		&i.Menu,
		&i.CategoryIds,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
