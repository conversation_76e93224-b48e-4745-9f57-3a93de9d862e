// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"context"
)

type Querier interface {
	CreateRefreshToken(ctx context.Context, arg *CreateRefreshTokenParams) error
	DeleteRefreshToken(ctx context.Context, userID uint64) error
	GetCategoryItemsByMenuIdAndShopId(ctx context.Context, arg *GetCategoryItemsByMenuIdAndShopIdParams) (*WebshopMenu, error)
	GetDeliveryPlatform(ctx context.Context, arg *GetDeliveryPlatformParams) (*DeliveryPlatform, error)
	GetRefreshToken(ctx context.Context, userID uint64) (*PosRefreshToken, error)
	GetShopByID(ctx context.Context, id uint64) (*Shop, error)
	GetTenantInfo(ctx context.Context, xTenantCode string) (*GetTenantInfoRow, error)
	GetTenants(ctx context.Context) ([]*Tenant, error)
	GetUserByEmail(ctx context.Context, email string) (*User, error)
	GetUserByID(ctx context.Context, id uint64) (*GetUserByIDRow, error)
	GetUsers(ctx context.Context) ([]*GetUsersRow, error)
}

var _ Querier = (*Queries)(nil)
