// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: jwt_query.sql

package db

import (
	"context"
	"time"
)

const createRefreshToken = `-- name: CreateRefreshToken :exec
INSERT INTO pos_refresh_tokens (user_id, token, expires_at) VALUES (?, ?, ?)
`

type CreateRefreshTokenParams struct {
	UserID    uint64    `json:"userId"`
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expiresAt"`
}

func (q *Queries) CreateRefreshToken(ctx context.Context, arg *CreateRefreshTokenParams) error {
	_, err := q.db.ExecContext(ctx, createRefreshToken, arg.UserID, arg.Token, arg.ExpiresAt)
	return err
}

const deleteRefreshToken = `-- name: DeleteRefreshToken :exec
DELETE FROM pos_refresh_tokens WHERE user_id = ?
`

func (q *Queries) DeleteRefreshToken(ctx context.Context, userID uint64) error {
	_, err := q.db.ExecContext(ctx, deleteRefreshToken, userID)
	return err
}

const getRefreshToken = `-- name: GetRefreshToken :one
SELECT user_id, token, expires_at, created_at, updated_at FROM pos_refresh_tokens WHERE user_id = ?
`

func (q *Queries) GetRefreshToken(ctx context.Context, userID uint64) (*PosRefreshToken, error) {
	row := q.db.QueryRowContext(ctx, getRefreshToken, userID)
	var i PosRefreshToken
	err := row.Scan(
		&i.UserID,
		&i.Token,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
