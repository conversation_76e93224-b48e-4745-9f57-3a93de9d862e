CREATE TABLE `tenants` (
  `id` bigint UNSIGNED NOT NULL,
  `host` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `port` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `database_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `x_tenant_code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `server_client_id` int NOT NULL,
  `server_client_secret` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `site_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `prd_domain` varchar(191) COLLAT<PERSON> utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `has_webshop` tinyint(1) NOT NULL DEFAULT '0',
  `enable_dusk` tinyint(1) NOT NULL DEFAULT '0',
  `dusk_port` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '9515',
  `sync_status` tinyint(1) NOT NULL DEFAULT '0',
  `sync_webshop_status` tinyint(1) NOT NULL DEFAULT '0',
  `has_multishop` tinyint(1) NOT NULL DEFAULT '0',
  `send_reports` tinyint(1) NOT NULL DEFAULT '0',
  `retry_authentication` tinyint(1) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;