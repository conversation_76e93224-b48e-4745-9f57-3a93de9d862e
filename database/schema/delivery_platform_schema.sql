CREATE TABLE `delivery_platform` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `platform_id` int DEFAULT NULL,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `webshop_brand_id` int DEFAULT NULL,
  `api_url` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `auth_url` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `api_parameters` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `logo` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `outlet_code` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `site_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `branch_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `access_token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `primary_color` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `can_upload` tinyint(1) NOT NULL DEFAULT '1',
  `auto_accepting` tinyint(1) NOT NULL DEFAULT '1',
  `franchise_id` int NOT NULL DEFAULT '1',
  `outlet_id` bigint DEFAULT NULL,
  `menu_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `menu_upload_status` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `store_status` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `available_from` datetime DEFAULT NULL,
  `tender_types` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_master` tinyint(1) NOT NULL DEFAULT '0',
  `parent_platform` int DEFAULT NULL,
  `prep_time` int DEFAULT NULL,
  `own_driver` tinyint(1) NOT NULL DEFAULT '0',
  `menu_published_at` datetime DEFAULT NULL,
  `webshop_setup_status` tinyint(1) NOT NULL DEFAULT '0',
  `has_cash_payment` tinyint(1) NOT NULL DEFAULT '0',
  `has_card_payment` tinyint(1) NOT NULL DEFAULT '1',
  `selected_menu` int DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci