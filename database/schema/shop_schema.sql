CREATE TABLE `shop` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `franchise_id` int NOT NULL,
  `code` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `branch` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_no` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `business_reg_no` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `socket_code` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE',
  `order_status` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ONLINE',
  `last_updated_menu` int DEFAULT NULL,
  `last_pos_synced` datetime DEFAULT NULL,
  `service_availability` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `minimum_amount_for_free_delivery` decimal(8,2) DEFAULT NULL,
  `minimum_amount_for_delivery` decimal(8,2) DEFAULT NULL,
  `one_time_promotion_value` decimal(4,2) DEFAULT NULL,
  `one_time_promotion_spend_amount` decimal(8,2) DEFAULT NULL,
  `latitude` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `longitude` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `google_location_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `one_time_promotion_type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `maximum_promotion_value` decimal(8,2) NOT NULL DEFAULT '0.00',
  `selected_menu` int DEFAULT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `country_code` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `timezone` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Europe/London',
  `currency` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '£',
  `currency_code` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'GBP',
  `print_receipt_accept` tinyint(1) NOT NULL DEFAULT '1',
  `number_of_receipt` int NOT NULL DEFAULT '1',
  `delivery_platform_enable` tinyint(1) NOT NULL DEFAULT '1',
  `run_test_order` tinyint(1) NOT NULL DEFAULT '0',
  `has_cash_payment` tinyint(1) NOT NULL DEFAULT '0',
  `has_card_payment` tinyint(1) NOT NULL DEFAULT '1',
  `tbl_commission_per_head` decimal(8,2) NOT NULL DEFAULT '0.00',
  `tbl_ref_commission_per_head` decimal(8,2) NOT NULL DEFAULT '0.00',
  `transfer_destination` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `delivergate_account` tinyint(1) NOT NULL DEFAULT '0',
  `printer_status` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `has_collection_points` tinyint(1) NOT NULL DEFAULT '0',
  `collection_points_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `user_interaction_status` tinyint(1) NOT NULL DEFAULT '0',
  `interaction_timeout` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `shop_code_unique` (`code`),
  KEY `shop_id_index` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci